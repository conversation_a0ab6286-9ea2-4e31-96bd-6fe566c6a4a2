# TanStack Table Infinite Loop Issues and Solutions

## Overview

Based on the analysis of your custom table implementation and TanStack Table documentation, I've identified several infinite loop issues and provided comprehensive solutions.

## Root Causes of Infinite Loops

### 1. **Unstable Data References**
**Problem**: Creating new object/array references on every render
**Example**: 
```typescript
// ❌ BAD: Creates new array reference on every render
const data = someData?.filter(d => d.isActive) ?? [];

// ❌ BAD: Unstable state updates
const [data, setData] = useState(() => actualData);
useEffect(() => {
    setData(actualData); // Triggers re-render if actualData changes
}, [actualData]);
```

**Solution**: Use `useMemo` for stable references
```typescript
// ✅ GOOD: Stable reference with useMemo
const data = useMemo(() => initialData, [initialData]);

// ✅ GOOD: Extract data properly
const { data, paginationMeta } = useMemo(() => {
    if (!initialData) return { data: [], paginationMeta: null };
    // ... extraction logic
}, [initialData, isServerSide]);
```

### 2. **Missing or Incorrect useEffect Dependencies**
**Problem**: Effects that depend on values not included in dependency array
**Example**:
```typescript
// ❌ BAD: Missing dependencies
useEffect(() => {
    table.getColumn(searchColumn)?.setFilterValue(searchValue);
}, [searchValue]); // Missing: table, searchColumn
```

**Solution**: Include all dependencies
```typescript
// ✅ GOOD: Complete dependencies
useEffect(() => {
    const timeoutId = setTimeout(() => {
        if (isServerSide && showSearch && onPaginationChange) {
            // Server-side logic
        } else if (showSearch && searchColumn && searchValue) {
            table.getColumn(searchColumn)?.setFilterValue(searchValue);
        }
    }, 300);
    return () => clearTimeout(timeoutId);
}, [
    searchValue,
    isServerSide,
    showSearch,
    searchColumn,
    serverParams,
    onPaginationChange,
    table,
]); // All dependencies included
```

### 3. **Unstable Callback Functions**
**Problem**: Creating new functions on every render
**Example**:
```typescript
// ❌ BAD: New function on every render
onSortingChange: (updater) => {
    const newSorting = typeof updater === "function" ? updater(sorting) : updater;
    setSorting(newSorting);
    onPaginationChange?.(newParams);
}
```

**Solution**: Use `useCallback` for stable function references
```typescript
// ✅ GOOD: Stable callback with useCallback
const handleSortingChange = useCallback((updater) => {
    const newSorting = typeof updater === "function" ? updater(sorting) : updater;
    setSorting(newSorting);
    if (isServerSide && onSortingChange) {
        onSortingChange(newSorting);
    }
}, [sorting, isServerSide, onSortingChange]);
```

## Specific Fixes Applied

### 1. **Fixed CustomTable Data Handling**
- Replaced separate `actualData` and `paginationMeta` with single memoized extraction
- Removed redundant state and useEffect for data synchronization
- Used stable references to prevent unnecessary re-renders

### 2. **Fixed Search Effect Dependencies**
- Added all missing dependencies to the search useEffect
- Maintained debouncing to prevent excessive API calls
- Ensured proper cleanup with timeout clearing

### 3. **Fixed DataTable Component**
- Replaced state-based data management with memoized data
- Updated drag handler to avoid state mutations
- Provided stable data reference

## Best Practices for TanStack Table

### 1. **Server-Side Configuration**
```typescript
const table = useReactTable({
    data,
    columns,
    // Enable manual modes for server-side
    manualSorting: isServerSide,
    manualFiltering: isServerSide,
    manualPagination: isServerSide,
    pageCount: isServerSide ? totalPages : undefined,
    // Only use client-side models when not server-side
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: isServerSide ? undefined : getFilteredRowModel(),
    getSortedRowModel: isServerSide ? undefined : getSortedRowModel(),
    getPaginationRowModel: isServerSide ? undefined : getPaginationRowModel(),
});
```

### 2. **Fuzzy Filtering Implementation**
```typescript
// Custom fuzzy filter
const fuzzyFilter = (row, columnId, value, addMeta) => {
    const itemRank = rankItem(row.getValue(columnId), value);
    addMeta({ itemRank });
    return itemRank.passed;
};

// Custom fuzzy sort
const fuzzySort = (rowA, rowB, columnId) => {
    let dir = 0;
    if (rowA.columnFiltersMeta[columnId]) {
        dir = compareItems(
            rowA.columnFiltersMeta[columnId]?.itemRank!,
            rowB.columnFiltersMeta[columnId]?.itemRank!
        );
    }
    return dir === 0 ? sortingFns.alphanumeric(rowA, rowB, columnId) : dir;
};

// Apply to table
const table = useReactTable({
    filterFns: { fuzzy: fuzzyFilter },
    sortingFns: { fuzzySort: fuzzySort },
    globalFilterFn: 'fuzzy',
    // ...
});
```

### 3. **Debounced Search**
```typescript
const [debouncedGlobalFilter, setDebouncedGlobalFilter] = useState(globalFilter);

useEffect(() => {
    const timeoutId = setTimeout(() => {
        setDebouncedGlobalFilter(globalFilter);
    }, 300);
    return () => clearTimeout(timeoutId);
}, [globalFilter]);

useEffect(() => {
    if (isServerSide && onGlobalFilterChange) {
        onGlobalFilterChange(debouncedGlobalFilter);
    }
}, [debouncedGlobalFilter, isServerSide, onGlobalFilterChange]);
```

### 4. **State Management**
```typescript
// Control specific states externally
const [sorting, setSorting] = useState([]);
const [columnFilters, setColumnFilters] = useState([]);
const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });

const table = useReactTable({
    state: {
        sorting,
        columnFilters,
        pagination,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: setPagination,
    // ...
});
```

## Performance Optimizations

### 1. **Memoization**
- Always memoize `data` and `columns` props
- Use `useMemo` for derived values
- Use `useCallback` for event handlers

### 2. **Conditional Row Models**
- Only include row models needed for your use case
- Disable client-side models when using server-side processing

### 3. **Debouncing**
- Implement debouncing for search and filter inputs
- Use appropriate delay (300ms is typically good)

## Testing Your Fixes

1. **Check for infinite re-renders**: Use React DevTools Profiler
2. **Monitor network requests**: Ensure server-side calls aren't excessive
3. **Test state changes**: Verify sorting, filtering, and pagination work correctly
4. **Performance testing**: Test with large datasets

## New Improved Component

I've created `apps/web/src/components/improved-table.tsx` that demonstrates all these best practices:

- ✅ Stable data and column references
- ✅ Proper useCallback for event handlers
- ✅ Complete useEffect dependencies
- ✅ Fuzzy filtering and sorting
- ✅ Server-side support with proper configuration
- ✅ Debounced search
- ✅ Performance optimizations

Use this as a reference for implementing similar patterns in your existing components.
