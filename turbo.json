{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}, "db:push": {"cache": false, "persistent": true}, "db:studio": {"cache": false, "persistent": true}, "db:migrate": {"cache": false, "persistent": true}, "db:generate": {"cache": false, "persistent": true}, "db:reset": {"cache": false, "persistent": true}, "db:seed": {"cache": false, "persistent": true}, "db:seed:basic": {"cache": false, "persistent": true}, "db:start": {"cache": false, "persistent": true}, "db:stop": {"cache": false, "persistent": true}, "db:watch": {"cache": false, "persistent": true}, "db:down": {"cache": false, "persistent": true}}}