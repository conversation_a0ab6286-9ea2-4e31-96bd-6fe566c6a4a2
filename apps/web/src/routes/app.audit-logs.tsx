import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { orpc } from "@/utils/orpc";

export const Route = createFileRoute("/app/audit-logs")({
	component: LogsPage,
});

function LogsPage() {
	const logs = useQuery(
		orpc.logs.paginate.queryOptions({
			input: {
				page: 1,
				limit: 10,
			},
		}),
	);
	console.log(logs);
	return <div>Hello "/app/logs"!</div>;
}
