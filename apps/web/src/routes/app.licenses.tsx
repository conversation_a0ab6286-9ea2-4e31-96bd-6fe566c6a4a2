import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { orpc } from "@/utils/orpc";

export const Route = createFileRoute("/app/licenses")({
	component: LicensesPage,
});

function LicensesPage() {
	const licenses = useQuery(
		orpc.licenses.paginate.queryOptions({
			input: {
				page: 1,
				limit: 10,
			},
		}),
	);
	console.log(licenses);
	return <div>Hello "/app/licenses"!</div>;
}
