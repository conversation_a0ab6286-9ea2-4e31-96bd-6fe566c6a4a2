import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { orpc } from "@/utils/orpc";

export const Route = createFileRoute("/app/payments")({
	component: PaymentsPage,
});

function PaymentsPage() {
	const payments = useQuery(
		orpc.payments.paginate.queryOptions({
			input: {
				page: 1,
				limit: 10,
			},
		}),
	);
	console.log(payments);

	return <div>Hello "/app/payments"!</div>;
}
