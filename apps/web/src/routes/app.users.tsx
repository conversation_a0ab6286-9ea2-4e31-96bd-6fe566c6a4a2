import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import type {
	Column,
	Row,
	RowSelectionState,
	SortingState,
	Table,
} from "@tanstack/react-table";

import { useMemo, useState } from "react";
import { useDebounce } from "use-debounce";
import type { z } from "zod";
import { Drag<PERSON>andle } from "@/components/drag-handle";
import SortableHeader from "@/components/sortable-header";
import { Checkbox } from "@/components/ui/checkbox";
import UserTable from "@/components/user-table";
import { orpc } from "@/utils/orpc";
import { UserModelSchema } from "../../../server/prisma/generated/zod/schemas";
export const Route = createFileRoute("/app/users")({
	component: UsersPage,
});

const UserSchema = UserModelSchema.omit({
	sessions: true,
	accounts: true,
	createdLicenses: true,
	sentInvitations: true,
	receivedInvitations: true,
	auditLogsAsActor: true,
	auditLogsAsTarget: true,
	processedRefunds: true,
	assignedTickets: true,
	supportMessages: true,
});

type UserType = z.infer<typeof UserSchema>;

function UsersPage() {
	const [page, setPage] = useState<number | undefined>(1);
	const [limit, setLimit] = useState<number | undefined>(10);
	const [globalFilter, setGlobalFilter] = useState<any>("");
	const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
	const [sorting, setSorting] = useState<SortingState>([]);
	const [search] = useDebounce(globalFilter, 1000);

	const { isPending, data: users } = useQuery(
		orpc.users.paginate.queryOptions({
			input: {
				page,
				limit,
				sortBy: sorting[0]?.id,
				sortOrder: sorting[0]?.desc ? "desc" : "asc",
				search,
			},
		}),
	);

	const handlePageSizeChange = (size: number) => {
		setLimit(size);
		setPage(1);
	};

	const handleGlobalFilterChange = (value: any) => {
		setGlobalFilter(value);
		setPage(1);
	};

	const columns = useMemo(
		() => [
			{
				id: "drag",
				header: () => null,
				cell: ({ row }: { row: Row<UserType> }) => (
					<DragHandle id={row.original.id} />
				),
			},
			{
				id: "select",
				header: ({ table }: { table: Table<UserType> }) => (
					<div className="flex items-center justify-center">
						<Checkbox
							checked={
								table.getIsAllPageRowsSelected() ||
								(table.getIsSomePageRowsSelected() && "indeterminate")
							}
							onCheckedChange={(value) =>
								table.toggleAllPageRowsSelected(!!value)
							}
							aria-label="Select all"
						/>
					</div>
				),
				cell: ({ row }: { row: Row<UserType> }) => (
					<div className="flex items-center justify-center">
						<Checkbox
							checked={row.getIsSelected()}
							onCheckedChange={(value) => row.toggleSelected(!!value)}
							aria-label="Select row"
						/>
					</div>
				),
				enableSorting: false,
				enableHiding: false,
			},
			{
				accessorKey: "name",
				enableSorting: true,
				header: ({ column }: { column: Column<UserType> }) => (
					<SortableHeader column={column} label="Name" />
				),
			},
			{
				accessorKey: "email",
				enableSorting: true,
				header: ({ column }: { column: Column<UserType> }) => (
					<SortableHeader column={column} label="Email" />
				),
			},
			{
				accessorKey: "role",
				header: "Role",
			},
		],
		[],
	);

	if (isPending) {
		return <div>Loading...</div>;
	}

	console.log(search);

	return (
		<div className="px-4 py-4">
			<UserTable
				isLoading={isPending}
				initialData={users}
				columns={columns}
				onPageChange={(page: number) => setPage(page)}
				onPageSizeChange={handlePageSizeChange}
				onRowSelectionChange={setRowSelection}
				rowSelection={rowSelection}
				sorting={sorting}
				onSortingChange={setSorting}
				onGlobalFilterChange={handleGlobalFilterChange}
				globalFilter={globalFilter}
			/>
		</div>
	);
}
