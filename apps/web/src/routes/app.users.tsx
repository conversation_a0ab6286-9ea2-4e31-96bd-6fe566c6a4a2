import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";

import { orpc } from "@/utils/orpc";4
export const Route = createFileRoute("/app/users")({
	component: RouteComponent,
});

function RouteComponent() {
	const users = useQuery(
		orpc.users.paginate.queryOptions({
			input: {
				page: 1,
				limit: 10,
			},
		}),
	);

	return (
		<div className="px-4 py-4">
			<>Users</>
		</div>
	);
}
