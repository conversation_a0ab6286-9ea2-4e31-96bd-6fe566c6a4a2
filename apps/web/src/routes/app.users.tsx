import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { CustomTable, DragHandle } from "@/components/custom-table";
import {
	Drawer,
	DrawerContent,
	DrawerHeader,
	DrawerTitle,
} from "@/components/ui/drawer";
import { orpc } from "@/utils/orpc";
export const Route = createFileRoute("/app/users")({
	component: RouteComponent,
});

function RouteComponent() {
	const users = useQuery(
		orpc.users.paginate.queryOptions({
			input: {
				page: 1,
				limit: 10,
			},
		}),
	);

	return (
		<div className="px-4 py-4">
			<CustomTable
				showSearch={true}
				searchPlaceholder="Search users..."
				searchColumn="name"
				defaultTab="all"
				tabs={[
					{
						value: "all",
						label: "All Users",
						badge: users.data?.meta.totalCount,
					},
					{
						value: "active",
						label: "Active",
						badge: users.data?.items.filter((u) => u.isActive).length,
					},
					{
						value: "inactive",
						label: "Inactive",
						badge: users.data?.items.filter((u) => !u.isActive).length,
					},
				]}
				data={users.data}
				isServerSide={true}
				loading={users.isLoading}
				onPaginationChange={() => {}}
				columns={[
					{
						id: "drag",
						header: () => null,
						cell: ({ row }) => <DragHandle id={row.original.id} />,
					},
					{ accessorKey: "name", header: "Name" },
					{ accessorKey: "status", header: "Status" },
					{ accessorKey: "email", header: "Email" },
					{ accessorKey: "role", header: "Role" },
					{ accessorKey: "isActive", header: "Active" },
				]}
				renderRowDetail={(row, close) => (
					<Drawer open onOpenChange={close}>
						<DrawerContent>
							<DrawerHeader>
								<DrawerTitle>Details for {row.original.name}</DrawerTitle>
							</DrawerHeader>
							<div className="p-4">
								<pre className="text-xs">
									{JSON.stringify(row.original, null, 2)}
								</pre>
							</div>
						</DrawerContent>
					</Drawer>
				)}
			/>
		</div>
	);
}
