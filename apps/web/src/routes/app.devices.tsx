import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { orpc } from "@/utils/orpc";

export const Route = createFileRoute("/app/devices")({
	component: DevicesPage,
});

function DevicesPage() {
	const devices = useQuery(
		orpc.devices.paginate.queryOptions({
			input: {
				page: 1,
				limit: 10,
			},
		}),
	);
	console.log(devices);
	return <div>Hello "/app/devices"!</div>;
}
