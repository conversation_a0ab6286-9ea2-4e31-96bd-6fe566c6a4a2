import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { orpc } from "@/utils/orpc";

export const Route = createFileRoute("/app/invitations")({
	component: InvitationsPage,
});

function InvitationsPage() {
	const invitations = useQuery(
		orpc.invitations.paginate.queryOptions({
			input: {
				page: 1,
				limit: 10,
			},
		}),
	);
	console.log(invitations);
	return <div>Hello "/app/invitations"!</div>;
}
