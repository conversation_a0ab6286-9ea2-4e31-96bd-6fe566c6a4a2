import { IconSearch } from "@tabler/icons-react";
import type { Table } from "@tanstack/react-table";
import type React from "react";
import { useId } from "react";
import { Input } from "./ui/input";

interface TableSearchProps {
	placeholder?: string;
	value: string;
	onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}
export default function TableSearch({
	placeholder = "Search...",
	value,
	onChange,
}: TableSearchProps) {
	return (
		<div className="relative">
			<IconSearch className="-translate-y-1/2 absolute top-1/2 left-2 size-4 text-muted-foreground" />
			<Input
				id={useId()}
				placeholder={placeholder}
				value={value}
				onChange={onChange}
				className="w-64 pl-8"
			/>
		</div>
	);
}
