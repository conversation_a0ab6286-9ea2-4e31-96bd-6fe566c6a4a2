import type { UniqueIdentifier } from "@dnd-kit/core";
import type { Column } from "@tanstack/react-table";
import { ArrowDown, ArrowUp } from "lucide-react";
import { Button } from "./ui/button";

interface SortableHeaderProps<T extends { id: UniqueIdentifier }> {
	column: Column<T>;
	label: string;
}
export default function SortableHeader<T extends { id: UniqueIdentifier }>({
	label,
	column,
}: SortableHeaderProps<T>) {
	return (
		<Button
			className="!p-0 cursor-pointer"
			variant="ghost"
			onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
		>
			{label}
			{column.getCanSort() && column.getIsSorted() ? (
				column.getIsSorted() === "desc" ? (
					<ArrowDown size={16} />
				) : (
					<ArrowUp size={16} />
				)
			) : null}
		</Button>
	);
}
