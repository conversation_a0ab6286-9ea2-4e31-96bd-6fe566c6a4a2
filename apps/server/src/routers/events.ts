import { WebhookEventModelSchema } from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const WebhookEventBaseSchema = WebhookEventModelSchema;
const WebhookEventListSchema = createListOutputSchema(WebhookEventBaseSchema);
export const events = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.webhookEvent.findMany();
	}),
	paginate: protectedProcedure
		.input(paginationSchema)
		.output(WebhookEventListSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.webhookEvent, input);
		}),

	get: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.webhookEvent.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
};
