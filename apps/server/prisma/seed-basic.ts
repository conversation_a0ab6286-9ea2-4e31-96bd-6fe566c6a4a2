import {
	randAlphaNumeric,
	randEmail,
	randFullName,
	randFutureDate,
	randNumber,
	randPastDate,
	randPhrase,
	randRecentDate,
	randText,
} from "@ngneat/falso";
import { createHash, randomBytes } from "crypto";
import { PrismaClient } from "prisma/generated/client";

const prisma = new PrismaClient();

// Helper functions
function generateLicenseKey(): string {
	const segments = Array.from({ length: 4 }, () =>
		randAlphaNumeric({ length: 4 }).join("").toUpperCase(),
	);
	return segments.join("-");
}

function generateDeviceHash(deviceId: string, salt: string): string {
	return createHash("sha256")
		.update(deviceId + salt)
		.digest("hex");
}

function generateSalt(): string {
	return randomBytes(16).toString("hex");
}

async function main() {
	console.log("🌱 Starting basic database seeding...");

	// Clear existing data
	console.log("🧹 Clearing existing data...");
	await prisma.supportMessage.deleteMany();
	await prisma.supportTicket.deleteMany();
	await prisma.auditLog.deleteMany();
	await prisma.rateLimit.deleteMany();
	await prisma.refundRequest.deleteMany();
	await prisma.device.deleteMany();
	await prisma.deviceExpansion.deleteMany();
	await prisma.license.deleteMany();
	await prisma.webhookEvent.deleteMany();
	await prisma.paymentIntent.deleteMany();
	await prisma.userInvitation.deleteMany();
	await prisma.session.deleteMany();
	await prisma.account.deleteMany();
	await prisma.verification.deleteMany();
	await prisma.user.deleteMany();

	console.log("👥 Creating admin user...");

	// Create one super admin user
	const adminUser = await prisma.user.create({
		data: {
			name: "Admin User",
			email: "<EMAIL>",
			emailVerified: true,
			role: "SUPER_ADMIN",
			isActive: true,
			lastLoginAt: randRecentDate(),
		},
	});

	console.log("💳 Creating payment intents...");

	// Create a few payment intents
	const paymentIntents = [];
	for (let i = 0; i < 3; i++) {
		const paymentIntent = await prisma.paymentIntent.create({
			data: {
				stripePaymentIntentId: `pi_test_${randAlphaNumeric({ length: 24 })}`,
				stripeCheckoutSessionId: `cs_test_${randAlphaNumeric({ length: 24 })}`,
				amount: [2999, 4999, 9999][i], // $29.99, $49.99, $99.99
				currency: "usd",
				status: "SUCCEEDED",
				paymentType: "LICENSE_PURCHASE",
				customerEmail: `customer${i + 1}@example.com`,
				customerName: `Customer ${i + 1}`,
				processedAt: randRecentDate(),
			},
		});
		paymentIntents.push(paymentIntent);
	}

	console.log("🔑 Creating licenses...");

	// Create licenses for each payment
	const licenses = [];
	for (let i = 0; i < paymentIntents.length; i++) {
		const paymentIntent = paymentIntents[i];
		const licenseType = ["TRIAL", "PRO", "ENTERPRISE"][i];

		const license = await prisma.license.create({
			data: {
				licenseKey: generateLicenseKey(),
				licenseType: licenseType as any,
				status: "ACTIVE",
				maxDevices: licenseType === "TRIAL" ? 1 : licenseType === "PRO" ? 2 : 5,
				usedDevices: 0,
				expiresAt: licenseType === "TRIAL" ? randFutureDate() : null,
				activatedAt: randPastDate(),
				customerEmail: paymentIntent.customerEmail,
				customerName: paymentIntent.customerName,
				createdBy: adminUser.id,
				paymentIntentId: paymentIntent.id,
				totalPaidAmount: paymentIntent.amount,
				emailSentAt: randRecentDate(),
				emailDeliveryStatus: "delivered",
			},
		});
		licenses.push(license);
	}

	console.log("📱 Creating devices...");

	// Create one device for each license
	const devices = [];
	for (let i = 0; i < licenses.length; i++) {
		const license = licenses[i];
		const salt = generateSalt();
		const deviceId = `device_${i + 1}`;
		const deviceHash = generateDeviceHash(deviceId, salt);

		const device = await prisma.device.create({
			data: {
				licenseId: license.id,
				deviceHash,
				salt,
				status: "ACTIVE",
				lastSeen: randRecentDate(),
				appVersion: "1.0.0",
				deviceName: `Test Device ${i + 1}`,
				deviceType: ["Desktop", "Laptop", "Mobile"][i],
				deviceModel: ["MacBook Pro", "Windows PC", "iPhone"][i],
				operatingSystem: ["macOS", "Windows 11", "iOS"][i],
				architecture: "x64",
				screenResolution: "1920x1080",
				totalMemory: "16GB",
			},
		});
		devices.push(device);

		// Update license used devices count
		await prisma.license.update({
			where: { id: license.id },
			data: { usedDevices: 1 },
		});
	}

	console.log("🎫 Creating support ticket...");

	// Create one support ticket
	const supportTicket = await prisma.supportTicket.create({
		data: {
			ticketId: "SNAP-2024-001",
			subject: "License activation issue",
			description: "Having trouble activating my license on a new device.",
			status: "OPEN",
			priority: "MEDIUM",
			category: "LICENSE",
			customerEmail: licenses[0].customerEmail,
			customerName: licenses[0].customerName,
			licenseKey: licenses[0].licenseKey,
			assignedTo: adminUser.id,
		},
	});

	console.log("💬 Creating support message...");

	// Create a message for the ticket
	await prisma.supportMessage.create({
		data: {
			ticketId: supportTicket.id,
			message:
				"Thank you for contacting support. We'll help you resolve this issue.",
			isInternal: false,
			authorId: adminUser.id,
		},
	});

	console.log("📊 Creating audit log...");

	// Create an audit log entry
	await prisma.auditLog.create({
		data: {
			action: "LICENSE_CREATED",
			licenseKey: licenses[0].licenseKey,
			licenseId: licenses[0].id,
			userId: adminUser.id,
			userEmail: adminUser.email,
			customerEmail: licenses[0].customerEmail,
			ipAddress: "127.0.0.1",
			userAgent: "Test User Agent",
		},
	});

	console.log("✅ Basic seeding completed successfully!");
	console.log("Created:");
	console.log("- 1 admin user (<EMAIL>)");
	console.log("- 3 payment intents");
	console.log("- 3 licenses (TRIAL, PRO, ENTERPRISE)");
	console.log("- 3 devices");
	console.log("- 1 support ticket");
	console.log("- 1 support message");
	console.log("- 1 audit log entry");
	console.log("\nTest credentials:");
	console.log("- Admin: <EMAIL>");
	console.log(
		"- Customer emails: <EMAIL>, <EMAIL>, <EMAIL>",
	);
}

main()
	.catch((e) => {
		console.error("❌ Basic seeding failed:", e);
		process.exit(1);
	})
	.finally(async () => {
		await prisma.$disconnect();
	});
